#!/usr/bin/env python3
"""
参考任务执行服务

基于参考任务的动作列表，让模型参考执行新的用例
使用LangGraph控制执行流转，完全独立的实现
"""

import shutil
from datetime import datetime
from typing import Dict, Any, List

from langgraph.graph import StateGraph, END
from langgraph.graph.state import CompiledStateGraph
from loguru import logger

from src.domain.reference_task.agent.reference_decision_agent import ReferenceDecisionAgent
from src.domain.ui_task.mobile.aggregate.agent.supervisor_agent import SupervisorAgent
from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager
from src.domain.ui_task.mobile.repo import task_stop_manager
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
from src.domain.ui_task.mobile.service.test_case_parser_service import add_step_numbers_if_needed
from src.schema.action_types import ExecutionStatus, ActionStatus


class ReferenceTaskService:
    """参考任务执行服务"""

    def __init__(self):
        """初始化服务"""
        self.decision_execution_agent = ReferenceDecisionAgent()
        self.supervisor_agent = SupervisorAgent()

    def execute_with_reference(
            self,
            task_id: str,
            request: Any
    ) -> Dict[str, Any]:
        """
        基于参考任务执行新的用例

        Args:
            task_id: 任务ID
            request: 参考任务执行请求

        Returns:
            执行结果
        """
        try:
            logger.info(f"[{task_id}] 🚀 Starting reference task execution...")

            # 获取参考任务的动作列表
            reference_actions = self._get_reference_task_actions(request.reference_task_id)
            if not reference_actions:
                return {
                    "success": False,
                    "message": f"No reference actions found for task {request.reference_task_id}",
                    "task_id": task_id
                }

            logger.info(f"[{task_id}] 📋 Found {len(reference_actions)} reference actions")

            # 初始化状态
            state = self._initialize_reference_state(
                task_id=task_id,
                request=request,
                reference_actions=reference_actions
            )

            # 创建执行图
            graph = self._create_execution_graph()

            # 在执行前更新任务状态为执行中
            task_persistence_service.update_task_status(
                task_id=task_id,
                status=ExecutionStatus.PROCESSING
            )

            # 执行图
            final_state = graph.invoke(state, {"recursion_limit": 200})

            # 更新数据库中的任务状态
            execution_status = final_state.get("execution_status", ExecutionStatus.FAILED.value)
            error_message = final_state.get("error_message")

            task_persistence_service.update_task_status(
                task_id=task_id,
                status=ExecutionStatus(execution_status),
                error_message=error_message
            )

            # 记录任务完成日志到execution_log（与普通UI任务保持一致）
            success = execution_status == ExecutionStatus.SUCCEED.value
            if execution_status not in [ExecutionStatus.TERMINATE.value]:
                # 只有在任务不是被停止的情况下才添加完成日志
                from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
                completion_log = ExecutionLogService.create_task_completion_log(success)
                task_persistence_service.append_execution_log_entries(task_id, [completion_log])
                logger.info(f"[{task_id}] 📝 Reference task completion log recorded: {'成功' if success else '失败'}")

            # 生成结果
            result = self._generate_execution_result(final_state)

            logger.info(f"[{task_id}] ✅ Reference task execution completed with status: {execution_status}")
            return result

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Reference task execution failed: {str(e)}")
            return {
                "success": False,
                "message": f"Reference task execution failed: {str(e)}",
                "task_id": task_id
            }

    @staticmethod
    def _get_reference_task_actions(reference_task_id: str) -> list:
        """
        获取参考任务的动作列表
        
        Args:
            reference_task_id: 参考任务ID
            
        Returns:
            动作列表
        """
        try:
            actions = task_persistence_service.get_task_actions(reference_task_id)
            if not actions:
                logger.warning(f"No actions found for reference task {reference_task_id}")
                return []

            # 转换为简化的动作描述列表，用于模型参考
            reference_actions = []
            for action in actions:
                action_info = {
                    "step_name": action.step_name,
                    "action": action.action,
                    "decision_content": action.decision_content,
                    "status": action.status
                }
                reference_actions.append(action_info)

            logger.info(f"Successfully retrieved {len(reference_actions)} reference actions")
            return reference_actions

        except Exception as e:
            logger.error(f"Error getting reference task actions: {str(e)}")
            return []

    def copy_task_as_success_case(
            self,
            new_task_id: str,
            source_task_id: str,
            action_ids: List[int]
    ) -> Dict[str, Any]:
        """
        将执行过的任务复制一份作为成功案例

        Args:
            new_task_id: 新的任务ID
            source_task_id: 被复制的任务ID
            action_ids: 要复制的动作ID列表（用户筛选后的）

        Returns:
            复制结果
        """
        try:
            logger.info(f"🔄 Starting to copy task {source_task_id} as success case with new task_id {new_task_id}")

            # 1. 获取源任务信息
            source_task = task_persistence_service.get_task_by_task_id(source_task_id)
            if not source_task:
                return {
                    "success": False,
                    "message": f"Source task {source_task_id} not found",
                    "task_id": new_task_id
                }

            # 2. 获取源任务的所有动作
            all_source_actions = task_persistence_service.get_task_actions(source_task_id)
            if not all_source_actions:
                return {
                    "success": False,
                    "message": f"No actions found for source task {source_task_id}",
                    "task_id": new_task_id
                }

            # 3. 筛选要复制的动作（根据action_ids）
            actions_to_copy = []
            for action in all_source_actions:
                if action.id in action_ids:
                    actions_to_copy.append(action)

            if not actions_to_copy:
                return {
                    "success": False,
                    "message": f"No valid actions found with provided action_ids",
                    "task_id": new_task_id
                }

            logger.info(f"📋 Found {len(actions_to_copy)} actions to copy from {len(all_source_actions)} total actions")

            # 4. 创建新任务记录
            new_task_data = self._prepare_new_task_data(source_task, new_task_id)
            new_task = self._create_new_task(new_task_data)
            if not new_task:
                return {
                    "success": False,
                    "message": f"Failed to create new task {new_task_id}",
                    "task_id": new_task_id
                }

            # 5. 复制动作和图片
            copied_actions_count = 0
            for action in actions_to_copy:
                if self._copy_action_with_images(action, new_task_id):
                    copied_actions_count += 1
                else:
                    logger.warning(f"⚠️ Failed to copy action {action.id}")

            logger.info(f"✅ Successfully copied {copied_actions_count}/{len(actions_to_copy)} actions")

            return {
                "success": True,
                "message": f"Successfully copied task as success case",
                "task_id": new_task_id,
                "copied_actions": copied_actions_count,
                "total_actions": len(actions_to_copy)
            }

        except Exception as e:
            logger.error(f"❌ Failed to copy task as success case: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to copy task: {str(e)}",
                "task_id": new_task_id
            }

    def _prepare_new_task_data(self, source_task, new_task_id: str) -> Dict[str, Any]:
        """
        准备新任务的数据

        Args:
            source_task: 源任务对象
            new_task_id: 新任务ID

        Returns:
            新任务数据字典
        """
        return {
            "task_id": new_task_id,
            "task_name": source_task.task_name,
            "execution_mode": source_task.execution_mode,
            "task_step_by_step": source_task.task_step_by_step,
            "task_aggregation_step": "废弃字段",
            "app_id": source_task.app_id,
            "agent_type": source_task.agent_type,
            "agent_config_id": source_task.agent_config_id,
            "device_id": source_task.device_id,
            "status": ExecutionStatus.SUCCEED.value,  # 标记为成功状态
            "task_expect_result": source_task.task_expect_result,
            "start_time": datetime.now(),
            "end_time": datetime.now(),
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }

    def _create_new_task(self, task_data: Dict[str, Any]):
        """
        创建新任务记录

        Args:
            task_data: 任务数据

        Returns:
            创建的任务对象或None
        """
        try:
            # 使用task_persistence_service创建任务
            from src.domain.ui_task.mobile.repo.dao import UITask

            task = UITask(**task_data)
            # 这里需要直接使用repository保存，因为task_persistence_service的create方法可能有其他逻辑
            saved_task = task_persistence_service.task_repo.create_task(task_data)

            if saved_task:
                logger.info(f"✅ New task created: {task_data['task_id']}")
                return saved_task
            else:
                logger.error(f"❌ Failed to create new task: {task_data['task_id']}")
                return None

        except Exception as e:
            logger.error(f"❌ Error creating new task: {str(e)}")
            return None

    def _copy_action_with_images(self, source_action, new_task_id: str) -> bool:
        """
        复制动作记录并处理图片复制

        Args:
            source_action: 源动作对象
            new_task_id: 新任务ID

        Returns:
            是否复制成功
        """
        try:
            new_image_path = None
            if source_action.image_path:
                new_image_path = self._copy_action_image(
                    source_action.image_path,
                    new_task_id
                )
                if not new_image_path:
                    logger.warning(f"⚠️ Failed to copy image for action {source_action.id}")

            # 2. 准备新动作数据
            action_data = {
                "task_id": new_task_id,
                "step_name": source_action.step_name,
                "start_time": source_action.start_time,
                "end_time": source_action.end_time,
                "status": ActionStatus.COMPLETED.value,  # 标记为已完成
                "error_message": source_action.error_message,
                "decision_content": source_action.decision_content,
                "action": source_action.action,
                "image_path": new_image_path,  # 使用新的图片路径
                "expect_result": source_action.expect_result,
                "verification_result": source_action.verification_result,
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }

            # 3. 创建新动作记录
            new_action = task_persistence_service.action_repo.create_action(action_data)
            if new_action:
                logger.info(f"✅ Action copied: {source_action.step_name} -> {new_action.id}")
                return True
            else:
                logger.error(f"❌ Failed to create new action for step: {source_action.step_name}")
                return False

        except Exception as e:
            logger.error(f"❌ Error copying action {source_action.id}: {str(e)}")
            return False

    def _copy_action_image(self, source_image_path: str, new_task_id: str) -> str | None:
        """
        复制动作的图片文件

        Args:
            source_image_path: 源图片路径 (格式: 20250901/087305e667134d3bac2ea07f269a2f03/screenshot_5_20250901_111943_377.png)
            new_task_id: 新任务ID

        Returns:
            新的图片路径，失败返回None
        """
        try:
            if not source_image_path:
                return None

            # 1. 解析源图片路径
            # 格式: 日期/任务id/图片文件名
            path_parts = source_image_path.split('/')
            if len(path_parts) != 3:
                logger.error(f"❌ Invalid source image path format: {source_image_path}")
                return None

            date_folder, old_task_id, image_filename = path_parts

            # 2. 获取源图片的完整路径
            source_full_path = screenshot_manager.get_screenshot_full_path(source_image_path)
            if not source_full_path.exists():
                logger.warning(f"⚠️ Source image file not found: {source_full_path}")
                return None

            # 3. 创建新的目录结构
            # 使用当前日期和新任务ID
            current_date = datetime.now().strftime("%Y%m%d")
            new_task_dir = screenshot_manager.get_task_screenshot_dir(new_task_id)

            # 4. 生成新的图片文件名（保持原有格式，但更新任务ID相关部分）
            # 将原文件名中的旧任务ID替换为新任务ID（如果存在）
            new_image_filename = image_filename.replace(old_task_id,
                                                        new_task_id) if old_task_id in image_filename else image_filename

            # 5. 构建新的图片路径
            new_image_file = new_task_dir / new_image_filename
            new_relative_path = f"{current_date}/{new_task_id}/{new_image_filename}"

            # 6. 复制图片文件
            shutil.copy2(source_full_path, new_image_file)

            # 7. 验证复制是否成功
            if new_image_file.exists() and new_image_file.stat().st_size > 0:
                logger.info(f"📸 Image copied: {source_image_path} -> {new_relative_path}")
                return new_relative_path
            else:
                logger.error(f"❌ Image copy verification failed: {new_image_file}")
                return None

        except Exception as e:
            logger.error(f"❌ Error copying image {source_image_path}: {str(e)}")
            return None

    @staticmethod
    def delete_success_case_action(action_id: int) -> Dict[str, Any]:
        """
        删除成功案例动作记录

        Args:
            action_id: 要删除的动作记录ID

        Returns:
            删除结果
        """
        try:
            logger.info(f"🗑️ Starting to delete success case action: action_id={action_id}")

            # 1. 获取动作记录
            action = task_persistence_service.action_repo.get_action_by_id(action_id)
            if not action:
                return {
                    "success": False,
                    "message": f"Action {action_id} not found"
                }

            # 2. 删除关联的图片文件（如果存在）
            if action.image_path:
                try:
                    image_full_path = screenshot_manager.get_screenshot_full_path(action.image_path)
                    if image_full_path.exists():
                        # 检查是否为文件（而不是目录）
                        if image_full_path.is_file():
                            image_full_path.unlink()
                            logger.info(f"📸 Deleted action image: {action.image_path}")
                        else:
                            logger.warning(f"⚠️ Image path is not a file: {action.image_path}")
                    else:
                        logger.info(f"📸 Image file already deleted or not found: {action.image_path}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to delete action image {action.image_path}: {str(e)}")
                    # 图片删除失败不应该阻止动作记录的删除，所以继续执行

            # 3. 删除动作记录
            success = task_persistence_service.action_repo.delete_action(action_id)
            if success:
                logger.info(f"✅ Success case action deleted: action_id={action_id}")
                return {
                    "success": True,
                    "message": "Success case action deleted successfully"
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to delete action {action_id} from database"
                }

        except Exception as e:
            logger.error(f"❌ Failed to delete success case action {action_id}: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to delete action: {str(e)}"
            }

    @staticmethod
    def delete_success_case_actions(action_ids: List[int]) -> Dict[str, Any]:
        """
        批量删除成功案例动作记录

        Args:
            action_ids: 要删除的动作记录ID列表

        Returns:
            删除结果
        """
        try:
            logger.info(f"🗑️ Starting to batch delete success case actions: action_ids={action_ids}")

            if not action_ids:
                return {
                    "success": True,
                    "message": "No actions to delete",
                    "deleted_count": 0,
                    "total_count": 0,
                    "failed_actions": []
                }

            deleted_count = 0
            failed_actions = []
            total_count = len(action_ids)

            # 逐个删除动作记录
            for action_id in action_ids:
                try:
                    # 复用单个删除的逻辑
                    result = ReferenceTaskService.delete_success_case_action(action_id)
                    if result["success"]:
                        deleted_count += 1
                        logger.info(f"✅ Action {action_id} deleted successfully")
                    else:
                        failed_actions.append({
                            "action_id": action_id,
                            "error": result["message"]
                        })
                        logger.warning(f"⚠️ Failed to delete action {action_id}: {result['message']}")
                except Exception as e:
                    failed_actions.append({
                        "action_id": action_id,
                        "error": str(e)
                    })
                    logger.error(f"❌ Exception while deleting action {action_id}: {str(e)}")

            # 判断整体结果
            if deleted_count == total_count:
                # 全部成功
                logger.info(f"✅ All {total_count} success case actions deleted successfully")
                return {
                    "success": True,
                    "message": f"All {total_count} actions deleted successfully",
                    "deleted_count": deleted_count,
                    "total_count": total_count,
                    "failed_actions": failed_actions
                }
            elif deleted_count > 0:
                # 部分成功
                logger.warning(f"⚠️ Partially successful: {deleted_count}/{total_count} actions deleted")
                return {
                    "success": True,
                    "message": f"Partially successful: {deleted_count}/{total_count} actions deleted",
                    "deleted_count": deleted_count,
                    "total_count": total_count,
                    "failed_actions": failed_actions
                }
            else:
                # 全部失败
                logger.error(f"❌ Failed to delete any actions: 0/{total_count} actions deleted")
                return {
                    "success": False,
                    "message": f"Failed to delete any actions: 0/{total_count} actions deleted",
                    "deleted_count": deleted_count,
                    "total_count": total_count,
                    "failed_actions": failed_actions
                }

        except Exception as e:
            logger.error(f"❌ Failed to batch delete success case actions: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to batch delete actions: {str(e)}",
                "deleted_count": 0,
                "total_count": len(action_ids) if action_ids else 0,
                "failed_actions": [{"action_id": aid, "error": str(e)} for aid in action_ids] if action_ids else []
            }

    @staticmethod
    def _parse_task_steps(task_description: str) -> list:
        """
        解析测试用例步骤，按行分割并添加序号

        Args:
            task_description: 测试用例描述

        Returns:
            步骤列表（带序号）
        """
        if not task_description:
            return []

        # 按换行符分割步骤
        lines = task_description.strip().split('\n')

        # 过滤空行并清理每行的空白字符
        steps = []
        for line in lines:
            cleaned_line = line.strip()
            if cleaned_line:  # 只保留非空行
                steps.append(cleaned_line)

        # 为步骤添加序号（如果需要的话）
        if steps:
            steps = add_step_numbers_if_needed(steps)

        logger.info(f"Parsed {len(steps)} steps from task description")
        for i, step in enumerate(steps, 1):
            logger.info(f"Step {i}: {step}")

        return steps

    def _initialize_reference_state(
            self,
            task_id: str,
            request: Any,
            reference_actions: list
    ) -> DeploymentState:
        """
        初始化参考任务执行状态

        Returns:
            初始化的状态
        """
        state = DeploymentState()

        # 从request中提取步骤信息
        task_description = '\n'.join([step.step for step in request.task_step_by_step])

        # 提取每个步骤的等待时间 - 引用成功案例执行直接使用TestCaseStep的wait_time字段
        step_wait_times = []
        for step in request.task_step_by_step:
            wait_time = step.wait_time if step.wait_time is not None else 1.0
            step_wait_times.append(wait_time)

        # Task related (与DeploymentState保持一致)
        state["task"] = task_description  # User input task description
        state["task_id"] = task_id

        # 解析测试用例步骤
        task_steps = self._parse_task_steps(task_description)
        state["task_steps"] = task_steps
        state["step_wait_times"] = step_wait_times

        state["completed"] = False
        state["execution_status"] = ExecutionStatus.PROCESSING.value
        state["retry_count"] = 0
        state["max_retries"] = 3  # 降低Service层面的重试次数，让Agent层面的动态重试逻辑生效
        state["step_failed"] = False
        state["error_message"] = None
        state[
            "test_case_name"] = request.task_name if request.task_name else f"Reference Task Based on {request.reference_task_id}"
        state["test_case_description"] = task_description
        state["expected_result"] = request.task_expect_result.text if request.task_expect_result else ""
        state["app_package"] = request.app_id
        state["execution_count"] = 0
        state["is_restart"] = request.is_restart
        state["app_foreground_check"] = request.app_foreground_check

        # Device related
        state["device"] = request.device.android.url
        state["device_type"] = "android"
        state["device_config"] = request.device.dict()

        # Agent related
        state["agent_config_id"] = request.agent_config_id

        # Verification related
        state["verification_mode"] = "reference_based"
        state["step_expected_results"] = []
        state["overall_expected_result"] = {}
        state["step_verification_results"] = []
        state["step_retry_counts"] = []
        state["max_step_retries"] = 3
        state["current_step_index"] = 0
        state["verification_failure_reason"] = None
        state["execution_blocked"] = False
        state["block_reason"] = None

        # Prompt customization
        state["app_name"] = request.app_name
        state["app_description"] = request.app_description
        state["ui_component_instructions"] = request.ui_component_instructions
        state["special_scenarios"] = request.special_scenarios

        # Supervisor related
        state["supervisor_state"] = None

        # Page information
        state["current_page"] = {}

        # Records and messages
        state["history"] = []
        state["messages"] = []
        state["decision_fields"] = None

        # Callback
        state["callback"] = None

        # 参考任务特有字段
        state["reference_task_id"] = request.reference_task_id
        state["reference_actions"] = reference_actions

        # 动态字段存储（用于print动作的动态字段对比功能）
        state["dynamic_fields"] = {}

        # 添加初始化记录
        state["history"].append({
            "action": "reference_task_initialization",
            "task_description": task_description,
            "reference_task_id": request.reference_task_id,
            "reference_actions_count": len(reference_actions),
            "timestamp": datetime.now().isoformat()
        })

        return state

    def _create_execution_graph(self) -> CompiledStateGraph:
        """
        创建基于LangGraph的执行图
        
        Returns:
            编译后的执行图
        """

        def route_next_action(state: DeploymentState) -> str:
            """路由下一个动作"""
            if state.get("completed", False):
                return "end"

            # 检查任务是否被停止
            if task_stop_manager.is_task_stopped(state["task_id"]):
                logger.info(f"[{state['task_id']}] 🛑 Task stopped by user, ending execution...")

                # 记录任务停止日志到execution_log
                try:
                    from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
                    stop_log = ExecutionLogService.create_task_stop_log()
                    task_persistence_service.append_execution_log_entries(state["task_id"], [stop_log])
                    logger.info(f"[{state['task_id']}] 📝 Reference task stop log recorded")
                except Exception as e:
                    logger.warning(f"[{state['task_id']}] ⚠️ Failed to record task stop log: {str(e)}")

                state["completed"] = True
                state["execution_status"] = ExecutionStatus.TERMINATE.value
                return "end"

            # 检查失败重试逻辑
            if state.get("step_failed", False):
                retry_count = state.get("retry_count", 0)
                max_retries = state.get("max_retries", 10)
                if retry_count >= max_retries:
                    logger.info(f"[{state['task_id']}] ⏭️ Task failed after {max_retries} retries, ending execution...")

                    # 记录任务失败日志到execution_log
                    try:
                        from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
                        error_message = state.get("error_message", f"任务重试{max_retries}次后失败")
                        failure_log = ExecutionLogService.create_system_log(f"任务执行失败: {error_message}")
                        task_persistence_service.append_execution_log_entries(state["task_id"], [failure_log])
                        logger.info(f"[{state['task_id']}] 📝 Reference task failure log recorded")
                    except Exception as e:
                        logger.warning(f"[{state['task_id']}] ⚠️ Failed to record task failure log: {str(e)}")

                    state["completed"] = True
                    state["execution_status"] = ExecutionStatus.FAILED.value
                    return "end"

            return "execute_step"

        # 创建图
        graph = StateGraph(DeploymentState)

        # 添加节点
        graph.add_node("execute_step", self._execute_reference_step_node)
        graph.set_entry_point("execute_step")

        # 添加条件边
        graph.add_conditional_edges(
            "execute_step",
            route_next_action,
            {
                "execute_step": "execute_step",
                "end": END
            }
        )

        return graph.compile()

    def _execute_reference_step_node(self, state: DeploymentState) -> DeploymentState:
        """
        执行参考任务步骤节点，支持分步执行

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        task_id = state["task_id"]

        try:
            logger.info(f"[{task_id}] 🎯 Executing reference-based step...")

            # 检查任务是否已完成
            if state.get("completed", False):
                return state

            # 获取步骤信息
            task_steps = state.get("task_steps", [])

            if not task_steps:
                logger.error(f"[{task_id}] ❌ No task steps found for step-by-step execution")
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.FAILED.value
                state["error_message"] = "No task steps found for step-by-step execution"
                return state

            # 初始化步骤执行状态
            current_step_index = state.get("current_step_index", 0)

            logger.info(f"[{task_id}] 📋 Found {len(task_steps)} steps, current step: {current_step_index + 1}")

            # 检查是否所有步骤都已完成
            if current_step_index >= len(task_steps):
                logger.info(f"[{task_id}] 🎉 All {len(task_steps)} steps completed successfully!")
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.SUCCEED.value
                return state

            # 执行当前步骤
            current_step = task_steps[current_step_index]
            logger.info(f"[{task_id}] 🎯 Executing step {current_step_index + 1}/{len(task_steps)}: {current_step}")

            # 设置当前步骤索引
            state["current_step_index"] = current_step_index

            # 执行监督检查
            state = self._execute_supervisor_check(state)

            # 检查监督是否阻止了执行
            if state.get("execution_blocked", False):
                logger.error(
                    f"[{task_id}] ❌ Execution blocked by supervisor: {state.get('block_reason', 'Unknown reason')}")
                state["step_failed"] = True
                state["retry_count"] = state.get("retry_count", 0) + 1
                state["error_message"] = f"Supervisor blocked execution: {state.get('block_reason', 'Unknown reason')}"
                return state

            # 使用参考决策Agent执行当前步骤
            step_result = self.decision_execution_agent.execute_step_with_reference(state, current_step)

            if step_result == "finished":
                logger.info(f"[{task_id}] ✅ AI判断任务已完成，直接结束任务")

                # 记录任务成功完成日志到execution_log
                try:
                    from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
                    success_log = ExecutionLogService.create_system_log(f"AI判断任务已完成: 当前执行到步骤{current_step_index + 1}/{len(task_steps)}")
                    task_persistence_service.append_execution_log_entries(task_id, [success_log])
                    logger.info(f"[{task_id}] 📝 任务成功完成日志已记录")
                except Exception as e:
                    logger.warning(f"[{task_id}] ⚠️ Failed to record task success log: {str(e)}")

                # 直接标记任务完成，不再检查步骤数量
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.SUCCEED.value
                return state

            elif step_result == "failed":
                logger.error(f"[{task_id}] ❌ Step {current_step_index + 1} failed")
                state["step_failed"] = True
                state["retry_count"] = state.get("retry_count", 0) + 1
                state["error_message"] = f"Step {current_step_index + 1} failed: {current_step}"

                # 记录Agent决策失败日志到execution_log
                try:
                    from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
                    agent_failure_log = ExecutionLogService.create_system_log(f"Agent决策失败: 步骤{current_step_index + 1} - {current_step}")
                    task_persistence_service.append_execution_log_entries(task_id, [agent_failure_log])
                    logger.info(f"[{task_id}] 📝 Agent failure log recorded")
                except Exception as e:
                    logger.warning(f"[{task_id}] ⚠️ Failed to record agent failure log: {str(e)}")

                # 当Agent主动返回failed时，直接设置任务为失败状态，不再重试
                state["completed"] = True
                state["execution_status"] = ExecutionStatus.FAILED.value
                logger.error(f"[{task_id}] ❌ Task marked as failed due to Agent decision")
                return state

            # step_result == "continue" 时继续执行当前步骤
            return state

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error in reference step execution: {str(e)}")
            state["step_failed"] = True
            state["retry_count"] = state.get("retry_count", 0) + 1
            state["error_message"] = str(e)
            return state

    def _execute_supervisor_check(self, state: DeploymentState) -> DeploymentState:
        """
        执行监督检查，确保应用状态正常

        Args:
            state: 当前状态

        Returns:
            更新后的状态
        """
        task_id = state["task_id"]
        app_package = state.get("app_package", "")

        try:
            logger.info(f"[{task_id}] 🔍 Executing supervisor check...")

            # 从state中获取app_foreground_check参数，默认为True
            app_foreground_check = state.get("app_foreground_check", True)

            # 执行监督检查
            supervised_state = self.supervisor_agent.supervise_execution(state, app_package, app_foreground_check)

            # 更新状态
            state.update(supervised_state)

            # 检查是否被阻止
            if state.get("execution_blocked", False):
                logger.warning(
                    f"[{task_id}] ⚠️ Supervisor blocked execution: {state.get('block_reason', 'Unknown reason')}")
            else:
                logger.info(f"[{task_id}] ✅ Supervisor check passed")

            return state

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Supervisor check failed: {str(e)}")
            # 监督检查失败时，不阻止执行，但记录错误
            state["supervisor_error"] = str(e)
            return state

    def _generate_execution_result(self, final_state: DeploymentState) -> Dict[str, Any]:
        """
        生成执行结果
        
        Args:
            final_state: 最终状态
            
        Returns:
            执行结果
        """
        execution_status = final_state.get("execution_status", ExecutionStatus.FAILED.value)
        success = execution_status == ExecutionStatus.SUCCEED.value

        result = {
            "success": success,
            "task_id": final_state["task_id"],
            "execution_status": execution_status,
            "execution_count": final_state.get("execution_count", 0),
            "reference_task_id": final_state.get("reference_task_id"),
            "message": final_state.get("error_message", "Task completed" if success else "Task failed")
        }

        return result


# 创建全局实例
reference_task_service = ReferenceTaskService()
